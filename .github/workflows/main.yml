name: <PERSON><PERSON>

# Controls when the action will run. 
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

env:
  MC_CALIB_PROD_DOCKER_IMG: bailool/mc-calib-prod:opencv4110
  MC_CALIB_DEV_DOCKER_IMG: bailool/mc-calib-dev:opencv4110

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  clang-format-lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2  # required to mount the Github Workspace to a volume

      - name: Run clang-format-lint
        uses: DoozyX/clang-format-lint-action@v0.18.1
        with:
          source: '.'
          exclude: './third_party ./external .git'
          extensions: 'h,cpp'
          clangFormatVersion: 11
  

  cppcheck:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Run cppcheck
        uses: addnab/docker-run-action@v3
        with:
          image: ${{env.MC_CALIB_DEV_DOCKER_IMG}}
          options: -v ${{ github.workspace }}:/home/<USER>
          run: |
            cppcheck MC-Calib/McCalib/include
            cppcheck MC-Calib/McCalib/src


  clang-tidy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Run clang-tidy
        uses: addnab/docker-run-action@v3
        with:
          image: ${{env.MC_CALIB_DEV_DOCKER_IMG}}
          options: -v ${{ github.workspace }}:/home/<USER>
          run: |
            mkdir MC-Calib/build && cd MC-Calib/build
            cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=ON -DCMAKE_BUILD_TYPE=Debug ..
            run-clang-tidy
  

  test-charuco-boards-generation:
    runs-on: ubuntu-latest
    needs:
      - clang-format-lint
      - cppcheck
      - clang-tidy
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Run charuco boards generation app
        uses: addnab/docker-run-action@v3
        with:
          image: ${{env.MC_CALIB_PROD_DOCKER_IMG}}
          options: -v ${{ github.workspace }}:/home/<USER>
          run: |
            mkdir MC-Calib/build && cd MC-Calib/build
            cmake -DCMAKE_BUILD_TYPE=Release ..
            make -j4
            ./apps/create_charuco_boards/generate_charuco ../tests/configs_for_end2end_tests/calib_param_synth_Scenario1.yml

      - name: Archive results artifacts
        uses: actions/upload-artifact@v4
        with:
          name: charuco_boards
          path: ${{ github.workspace }}/build/charuco_boards


  unit-tests-with-sanitizers:
    if: ${{ false }}  # TODO: disable for now until understand how to suppress leaks from external libraries like OpenCV
    runs-on: ubuntu-latest
    needs:
      - clang-format-lint
      - cppcheck
      - clang-tidy
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Checkout MC-Calib-data
        uses: actions/checkout@v4
        with:
          repository: BAILOOL/MC-Calib-data
          path: ${{ github.workspace }}/data
          lfs: 'true'

      - name: Unzip MC-Calib-data
        run: tar xf ${{ github.workspace }}/data/Blender_Images.tar.gz -C ${{ github.workspace }}/data
        
      - name: Run tests
        uses: addnab/docker-run-action@v3
        with:
          image: ${{env.MC_CALIB_DEV_DOCKER_IMG}}
          options: -v ${{ github.workspace }}:/home/<USER>
          run: |
            mkdir MC-Calib/build && cd MC-Calib/build
            cmake -DCMAKE_BUILD_TYPE=Debug -DUSE_SANITIZERS=ON ..
            make -j4
            ./tests/boost_tests_run


  unit-tests-with-valgrind:
    runs-on: ubuntu-latest
    needs:
      - clang-format-lint
      - cppcheck
      - clang-tidy
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Checkout MC-Calib-data
        uses: actions/checkout@v4
        with:
          repository: BAILOOL/MC-Calib-data
          path: ${{ github.workspace }}/data
          lfs: 'true'

      - name: Unzip MC-Calib-data
        run: tar xf ${{ github.workspace }}/data/Blender_Images.tar.gz -C ${{ github.workspace }}/data
        
      - name: Run tests
        uses: addnab/docker-run-action@v3
        with:
          image: ${{env.MC_CALIB_DEV_DOCKER_IMG}}
          options: -v ${{ github.workspace }}:/home/<USER>
          run: |
            mkdir MC-Calib/build && cd MC-Calib/build
            cmake -DCMAKE_BUILD_TYPE=Debug -DUSE_SANITIZERS=OFF ..
            make -j4
            valgrind --leak-check=full \
              --leak-check=full \
              --track-origins=yes \
              --show-reachable=yes \
              --error-limit=no \
              --gen-suppressions=all \
              --verbose \
              --log-file=valgrind-out.txt \
              --suppressions=../tests/valgrind_suppress/opencv_valgrind.supp \
              --suppressions=../tests/valgrind_suppress/opencv_valgrind_3rdparty.supp \
              --suppressions=../tests/valgrind_suppress/boost_valgrind.supp \
              ./apps/calibrate/calibrate ../tests/configs_for_end2end_tests/calib_param_synth_Scenario1.yml


  unit-tests-in-release-mode-opencv4110:
    runs-on: ubuntu-latest
    needs:
      - clang-format-lint
      - cppcheck
      - clang-tidy
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Checkout MC-Calib-data
        uses: actions/checkout@v4
        with:
          repository: BAILOOL/MC-Calib-data
          path: ${{ github.workspace }}/data
          lfs: 'true'

      - name: Unzip MC-Calib-data
        run: tar xf ${{ github.workspace }}/data/Blender_Images.tar.gz -C ${{ github.workspace }}/data
        
      - name: Run tests
        uses: addnab/docker-run-action@v3
        with:
          image: ${{env.MC_CALIB_PROD_DOCKER_IMG}}
          options: -v ${{ github.workspace }}:/home/<USER>
          run: |
            mkdir MC-Calib/build && cd MC-Calib/build
            cmake -DCMAKE_BUILD_TYPE=Release ..
            make -j4
            ./tests/boost_tests_run

      - name: Collect results
        run: |
          cd ${{ github.workspace }}/data/Blender_Images
          mkdir Results_Blender_Sequences

          mkdir Results_Blender_Sequences/Scenario_1
          cp -r Scenario_1/Results Results_Blender_Sequences/Scenario_1
          cp Scenario_1/GroundTruth.yml Results_Blender_Sequences/Scenario_1

          mkdir Results_Blender_Sequences/Scenario_2
          cp -r Scenario_2/Results Results_Blender_Sequences/Scenario_2
          cp Scenario_2/GroundTruth.yml Results_Blender_Sequences/Scenario_2

          mkdir Results_Blender_Sequences/Scenario_3
          cp -r Scenario_3/Results Results_Blender_Sequences/Scenario_3
          cp Scenario_3/GroundTruth.yml Results_Blender_Sequences/Scenario_3

          mkdir Results_Blender_Sequences/Scenario_4
          cp -r Scenario_4/Results Results_Blender_Sequences/Scenario_4
          cp Scenario_4/GroundTruth.yml Results_Blender_Sequences/Scenario_4

          mkdir Results_Blender_Sequences/Scenario_5
          cp -r Scenario_5/Results Results_Blender_Sequences/Scenario_5
          cp Scenario_5/GroundTruth.yml Results_Blender_Sequences/Scenario_5

      - name: Archive results artifacts
        uses: actions/upload-artifact@v4
        with:
          name: results_blender_sequences
          path: ${{ github.workspace }}/data/Blender_Images/Results_Blender_Sequences

  unit-tests-in-release-mode-opencv455:
    runs-on: ubuntu-latest
    needs:
      - clang-format-lint
      - cppcheck
      - clang-tidy
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Checkout MC-Calib-data
        uses: actions/checkout@v4
        with:
          repository: BAILOOL/MC-Calib-data
          path: ${{ github.workspace }}/data
          lfs: 'true'

      - name: Unzip MC-Calib-data
        run: tar xf ${{ github.workspace }}/data/Blender_Images.tar.gz -C ${{ github.workspace }}/data

      - name: Run tests
        uses: addnab/docker-run-action@v3
        with:
          image: bailool/mc-calib-prod:opencv455
          options: -v ${{ github.workspace }}:/home/<USER>
          run: |
            mkdir MC-Calib/build && cd MC-Calib/build
            cmake -DCMAKE_BUILD_TYPE=Release ..
            make -j4
            ./tests/boost_tests_run

  unit-tests-in-release-mode-opencv420:
    runs-on: ubuntu-latest
    needs:
      - clang-format-lint
      - cppcheck
      - clang-tidy
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Checkout MC-Calib-data
        uses: actions/checkout@v4
        with:
          repository: BAILOOL/MC-Calib-data
          path: ${{ github.workspace }}/data
          lfs: 'true'

      - name: Unzip MC-Calib-data
        run: tar xf ${{ github.workspace }}/data/Blender_Images.tar.gz -C ${{ github.workspace }}/data

      - name: Run tests
        uses: addnab/docker-run-action@v3
        with:
          image: bailool/mc-calib-prod:opencv420
          options: -v ${{ github.workspace }}:/home/<USER>
          run: |
            mkdir MC-Calib/build && cd MC-Calib/build
            cmake -DCMAKE_BUILD_TYPE=Release ..
            make -j4
            ./tests/boost_tests_run

  code-coverage-testing:
    runs-on: ubuntu-latest
    needs:
      - clang-format-lint
      - cppcheck
      - clang-tidy
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Checkout MC-Calib-data
        uses: actions/checkout@v4
        with:
          repository: BAILOOL/MC-Calib-data
          path: ${{ github.workspace }}/data
          lfs: 'true'

      - name: Unzip MC-Calib-data
        run: tar xf ${{ github.workspace }}/data/Blender_Images.tar.gz -C ${{ github.workspace }}/data
        
      - name: Run tests
        uses: addnab/docker-run-action@v3
        with:
          image: ${{env.MC_CALIB_DEV_DOCKER_IMG}}
          options: -v ${{ github.workspace }}:${{ github.workspace }}
          run: |
            mkdir ${{ github.workspace }}/build && cd ${{ github.workspace }}/build
            cmake -DCMAKE_BUILD_TYPE=Debug -DENABLE_COVERAGE=true ..
            make -j4
            ./tests/boost_tests_run && make coverage

      - name: Upload code coverage results
        uses: actions/upload-artifact@v4
        with:
          name: code-coverage-report
          path: ${{ github.workspace }}/build/coverage

      - name: Setup LCOV for report to PR
        uses: hrishikesh-kadam/setup-lcov@v1
      
      - name: Report code coverage to PR
        uses: zgosalvez/github-actions-report-lcov@v3
        with:
          coverage-files: build/coverage.info
          minimum-coverage: 77
          artifact-name: code-coverage-report-with-github-actions
          github-token: ${{ secrets.GITHUB_TOKEN }}
          update-comment: true


  python-utils-linters:
    runs-on: ubuntu-latest
    needs:
      - unit-tests-in-release-mode-opencv4110
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Run python linters
        uses: addnab/docker-run-action@v3
        with:
          image: ${{env.MC_CALIB_DEV_DOCKER_IMG}}
          options: -v ${{ github.workspace }}:/home/<USER>
          run: |
            cd MC-Calib/python_utils
            ./format.sh
            ./test.sh


  python-utils:
    runs-on: ubuntu-latest
    needs:
      - python-utils-linters
      - unit-tests-in-release-mode-opencv4110
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Download calibration results
        uses: actions/download-artifact@v4
        with:
          name: results_blender_sequences
          path: ${{ github.workspace }}/data

      - name: Run python utils
        uses: addnab/docker-run-action@v3
        with:
          image: ${{env.MC_CALIB_PROD_DOCKER_IMG}}
          options: -v ${{ github.workspace }}:/home/<USER>
          run: |
            cd MC-Calib/python_utils
            python3 post_calibration_analysis.py -d /home/<USER>/data/Scenario_1/Results/ /home/<USER>/data/Scenario_2/Results/ /home/<USER>/data/Scenario_3/Results/ /home/<USER>/data/Scenario_4/Results/ /home/<USER>/data/Scenario_5/Results/

      - name: Archive results artifacts
        uses: actions/upload-artifact@v4
        with:
          name: results_blender_sequences_with_python_utils
          path: ${{ github.workspace }}/data