### System information (version)

* Operating System / Platform => ❔
* OpenCV => ❔
* Ceres => ❔
* Boost => ❔
* C++ => ❔
* Compiler => ❔

### Vision system

* Number of cameras => ❔
* Types of cameras => ❔ (perspective, fisheye, hybrid)
* Multicamera configurations => ❔ (overlapping, non-overlapping, converging)
* Configuration file => ❔ (i.e. `*.yml`)
* Image sequences => ❔
    - number of images per camera
    - if relevant and possible, please share image sequences

### Describe the issue / bug

Please provide a clear and concise description of what the issue / bug is.