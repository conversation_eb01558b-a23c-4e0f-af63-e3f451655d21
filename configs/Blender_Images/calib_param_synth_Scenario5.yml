%YAML:1.0

######################################## Boards Parameters ###################################################
number_x_square: 5         # number of squares in the X direction
number_y_square: 5         # number of squares the Y direction
resolution_x: 500          # horizontal resolution in pixel
resolution_y: 500          # vertical resolution in pixel
length_square: 0.04        # parameters on the marker (can be kept as it is)
length_marker: 0.03        # parameters on the marker (can be kept as it is)
number_board: 6            # number of boards used for calibration (for overlapping camera 1 is enough ...)
boards_index: [0,2,3,4,6,7]           # leave it empty [] if the board index are ranging from zero to number_board; example of usage boards_index: [0,10] <-- only two board with index 0/10
square_size: 0.192         # size of each square of the board in cm/mm/whatever you want

############# Boards Parameters for different board size (leave empty if all boards have the same size) #################
number_x_square_per_board: []
number_y_square_per_board: []
square_size_per_board: []

######################################## Camera Parameters ###################################################
distortion_model: 0             # 0:Brown (perspective) // 1: Kannala (fisheye)
distortion_per_camera : []         # specify the model per camera, #leave "distortion_per_camera" empty [] if they all follow the same model (make sure that the vector is as long as cameras nb)
number_camera: 4           # number of cameras in the rig to calibrate
refine_corner: 1           # activate or deactivate the corner refinement
min_perc_pts: 0.5           # min percentage of points visible to assume a good detection

cam_params_path: "None"   # file with cameras intrinsics to initialize the intrinsic, write "None" if no initialization available 
fix_intrinsic: 0 #if 1 then the intrinsic parameters will not be estimated nor refined (initial value needed)

######################################## Images Parameters ###################################################
root_path: "../data/Blender_Images/Scenario_5/Images"
cam_prefix: "Cam_"
keypoints_path: "../data/Blender_Images/Scenario_5/Results/detected_keypoints_data.yml"  

######################################## Optimization Parameters #############################################
quaternion_averaging: 1    # use Quaternion Averaging or median for average rotation
ransac_threshold: 10       # RANSAC threshold in pixel (keep it high just to remove strong outliers)
number_iterations: 1000    # Max number of iterations for the non linear refinement

######################################## Hand-eye method #############################################
he_approach: 0 #0: bootstrapped he technique, 1: traditional he

######################################## Output Parameters ###################################################
save_path: "../data/Blender_Images/Scenario_5/Results"
save_detection: 0
save_reprojection: 0
camera_params_file_name: "" # "name.yml"
